from flask import Flask, request, jsonify
from flask_cors import CORS
import os
from dotenv import load_dotenv
import logging
from datetime import datetime

# Import our AI modules
from services.job_matcher import JobMatcher
from services.skill_analyzer import SkillAnalyzer
from services.recommendation_engine import RecommendationEngine
from services.profile_analyzer import ProfileAnalyzer

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize AI services
job_matcher = JobMatcher()
skill_analyzer = SkillAnalyzer()
recommendation_engine = RecommendationEngine()
profile_analyzer = ProfileAnalyzer()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'service': 'WorkWise AI Service'
    })

@app.route('/match/freelancers', methods=['POST'])
def match_freelancers():
    """Find matching freelancers for a job"""
    try:
        data = request.get_json()
        
        if not data or 'job_data' not in data:
            return jsonify({'error': 'Job data is required'}), 400
        
        job_data = data['job_data']
        job_id = data.get('job_id')
        
        # Get freelancer matches using AI
        matches = job_matcher.find_freelancer_matches(job_data, job_id)
        
        return jsonify({
            'matches': matches,
            'total_matches': len(matches),
            'job_id': job_id,
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in match_freelancers: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/match/jobs', methods=['POST'])
def match_jobs():
    """Find matching jobs for a freelancer"""
    try:
        data = request.get_json()
        
        if not data or 'freelancer_data' not in data:
            return jsonify({'error': 'Freelancer data is required'}), 400
        
        freelancer_data = data['freelancer_data']
        freelancer_id = data.get('freelancer_id')
        
        # Get job matches using AI
        matches = job_matcher.find_job_matches(freelancer_data, freelancer_id)
        
        return jsonify({
            'matches': matches,
            'total_matches': len(matches),
            'freelancer_id': freelancer_id,
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in match_jobs: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/analyze/job', methods=['POST'])
def analyze_job():
    """Analyze job posting and extract insights"""
    try:
        data = request.get_json()
        
        if not data or 'job_data' not in data:
            return jsonify({'error': 'Job data is required'}), 400
        
        job_data = data['job_data']
        
        # Analyze job using AI
        analysis = {
            'skills_extracted': skill_analyzer.extract_skills_from_text(
                job_data.get('title', '') + ' ' + job_data.get('description', '')
            ),
            'complexity_score': job_matcher.calculate_job_complexity(job_data),
            'market_insights': recommendation_engine.get_market_insights(job_data),
            'suggested_budget': recommendation_engine.suggest_budget_range(job_data),
            'estimated_duration': recommendation_engine.estimate_project_duration(job_data)
        }
        
        return jsonify({
            'analysis': analysis,
            'job_id': data.get('job_id'),
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in analyze_job: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/analyze/profile', methods=['POST'])
def analyze_profile():
    """Analyze freelancer profile and provide recommendations"""
    try:
        data = request.get_json()
        
        if not data or 'profile_data' not in data:
            return jsonify({'error': 'Profile data is required'}), 400
        
        profile_data = data['profile_data']
        
        # Analyze profile using AI
        analysis = profile_analyzer.analyze_profile(profile_data)
        
        return jsonify({
            'analysis': analysis,
            'user_id': data.get('user_id'),
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in analyze_profile: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/recommendations/skills', methods=['POST'])
def recommend_skills():
    """Recommend skills based on user profile and market trends"""
    try:
        data = request.get_json()
        
        if not data or 'user_data' not in data:
            return jsonify({'error': 'User data is required'}), 400
        
        user_data = data['user_data']
        
        # Get skill recommendations
        recommendations = recommendation_engine.recommend_skills(user_data)
        
        return jsonify({
            'recommendations': recommendations,
            'user_id': data.get('user_id'),
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in recommend_skills: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/recommendations/pricing', methods=['POST'])
def recommend_pricing():
    """Recommend pricing based on skills, experience, and market data"""
    try:
        data = request.get_json()
        
        if not data or 'freelancer_data' not in data:
            return jsonify({'error': 'Freelancer data is required'}), 400
        
        freelancer_data = data['freelancer_data']
        
        # Get pricing recommendations
        pricing = recommendation_engine.recommend_pricing(freelancer_data)
        
        return jsonify({
            'pricing_recommendations': pricing,
            'user_id': data.get('user_id'),
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in recommend_pricing: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/train/model', methods=['POST'])
def train_model():
    """Retrain AI models with new data"""
    try:
        data = request.get_json()
        
        model_type = data.get('model_type', 'all')
        
        # Trigger model retraining
        if model_type == 'all' or model_type == 'matcher':
            job_matcher.retrain_model()
        
        if model_type == 'all' or model_type == 'recommender':
            recommendation_engine.retrain_model()
        
        return jsonify({
            'message': f'Model retraining initiated for: {model_type}',
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in train_model: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/analytics/performance', methods=['GET'])
def get_performance_analytics():
    """Get AI model performance analytics"""
    try:
        analytics = {
            'job_matcher': job_matcher.get_performance_metrics(),
            'recommendation_engine': recommendation_engine.get_performance_metrics(),
            'skill_analyzer': skill_analyzer.get_performance_metrics(),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        return jsonify(analytics)
        
    except Exception as e:
        logger.error(f"Error in get_performance_analytics: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    logger.info(f"Starting WorkWise AI Service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
