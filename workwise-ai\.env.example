# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
FLASK_DEBUG=True

# Database Configuration
DATABASE_URL=mysql://workwise:password@localhost:3306/workwise

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# AI Service Configuration
AI_MODEL_PATH=./models
OPENAI_API_KEY=your_openai_api_key_here

# Logging
LOG_LEVEL=INFO

# Security
SECRET_KEY=your_secret_key_here

# External APIs
BACKEND_API_URL=http://localhost:8000/api
BACKEND_API_KEY=your_backend_api_key
