<?php

namespace App\Http\Controllers;

use App\Models\Job;
use App\Models\Bid;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;

class JobController extends Controller
{
    /**
     * Get all jobs with filtering and pagination
     */
    public function index(Request $request)
    {
        $query = Job::with(['employer', 'skills', 'bids'])
                   ->where('status', 'active');

        // Apply filters
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        if ($request->has('budget_min')) {
            $query->where('budget_min', '>=', $request->budget_min);
        }

        if ($request->has('budget_max')) {
            $query->where('budget_max', '<=', $request->budget_max);
        }

        if ($request->has('skills')) {
            $skills = is_array($request->skills) ? $request->skills : explode(',', $request->skills);
            $query->whereHas('skills', function ($q) use ($skills) {
                $q->whereIn('name', $skills);
            });
        }

        if ($request->has('location')) {
            $query->where('location', 'like', '%' . $request->location . '%');
        }

        if ($request->has('job_type')) {
            $query->where('job_type', $request->job_type);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $jobs = $query->paginate($request->get('per_page', 15));

        return response()->json($jobs);
    }

    /**
     * Get a specific job
     */
    public function show($id)
    {
        $job = Job::with(['employer', 'skills', 'bids.freelancer'])
                  ->findOrFail($id);

        return response()->json($job);
    }

    /**
     * Create a new job
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string|max:100',
            'job_type' => 'required|in:fixed,hourly',
            'budget_min' => 'required|numeric|min:0',
            'budget_max' => 'required|numeric|min:0|gte:budget_min',
            'deadline' => 'required|date|after:today',
            'skills' => 'required|array|min:1',
            'skills.*' => 'string|max:50',
            'location' => 'nullable|string|max:255',
            'experience_level' => 'required|in:entry,intermediate,expert',
            'project_duration' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $job = Job::create([
            'employer_id' => $request->user()->id,
            'title' => $request->title,
            'description' => $request->description,
            'category' => $request->category,
            'job_type' => $request->job_type,
            'budget_min' => $request->budget_min,
            'budget_max' => $request->budget_max,
            'deadline' => $request->deadline,
            'location' => $request->location,
            'experience_level' => $request->experience_level,
            'project_duration' => $request->project_duration,
            'status' => 'active',
        ]);

        // Attach skills
        $job->skills()->sync($request->skills);

        // Trigger AI matching
        $this->triggerAIMatching($job);

        return response()->json([
            'message' => 'Job created successfully',
            'job' => $job->load(['skills'])
        ], 201);
    }

    /**
     * Update a job
     */
    public function update(Request $request, $id)
    {
        $job = Job::findOrFail($id);

        // Check if user owns the job
        if ($job->employer_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'category' => 'sometimes|string|max:100',
            'job_type' => 'sometimes|in:fixed,hourly',
            'budget_min' => 'sometimes|numeric|min:0',
            'budget_max' => 'sometimes|numeric|min:0|gte:budget_min',
            'deadline' => 'sometimes|date|after:today',
            'skills' => 'sometimes|array|min:1',
            'skills.*' => 'string|max:50',
            'location' => 'nullable|string|max:255',
            'experience_level' => 'sometimes|in:entry,intermediate,expert',
            'project_duration' => 'nullable|string|max:100',
            'status' => 'sometimes|in:active,paused,closed,completed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $job->update($request->only([
            'title', 'description', 'category', 'job_type', 'budget_min',
            'budget_max', 'deadline', 'location', 'experience_level',
            'project_duration', 'status'
        ]));

        if ($request->has('skills')) {
            $job->skills()->sync($request->skills);
        }

        return response()->json([
            'message' => 'Job updated successfully',
            'job' => $job->fresh(['skills'])
        ]);
    }

    /**
     * Delete a job
     */
    public function destroy(Request $request, $id)
    {
        $job = Job::findOrFail($id);

        // Check if user owns the job
        if ($job->employer_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check if job has active bids
        if ($job->bids()->where('status', 'pending')->exists()) {
            return response()->json([
                'message' => 'Cannot delete job with pending bids'
            ], 400);
        }

        $job->delete();

        return response()->json([
            'message' => 'Job deleted successfully'
        ]);
    }

    /**
     * Search jobs
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');
        
        $jobs = Job::with(['employer', 'skills'])
                   ->where('status', 'active')
                   ->where(function ($q) use ($query) {
                       $q->where('title', 'like', "%{$query}%")
                         ->orWhere('description', 'like', "%{$query}%")
                         ->orWhere('category', 'like', "%{$query}%");
                   })
                   ->orderBy('created_at', 'desc')
                   ->paginate(15);

        return response()->json($jobs);
    }

    /**
     * Get AI-powered job matches for a freelancer
     */
    public function getMatches(Request $request, $jobId)
    {
        $job = Job::findOrFail($jobId);
        
        try {
            // Call AI service for freelancer matching
            $response = Http::post(config('services.ai.url') . '/match/freelancers', [
                'job_id' => $job->id,
                'job_data' => [
                    'title' => $job->title,
                    'description' => $job->description,
                    'skills' => $job->skills->pluck('name')->toArray(),
                    'category' => $job->category,
                    'experience_level' => $job->experience_level,
                    'budget_min' => $job->budget_min,
                    'budget_max' => $job->budget_max,
                ]
            ]);

            if ($response->successful()) {
                return response()->json($response->json());
            }
        } catch (\Exception $e) {
            // Fallback to basic matching if AI service is unavailable
            return $this->basicFreelancerMatching($job);
        }

        return response()->json([
            'matches' => [],
            'message' => 'AI matching service unavailable'
        ]);
    }

    /**
     * Trigger AI matching for new job
     */
    private function triggerAIMatching($job)
    {
        try {
            Http::post(config('services.ai.url') . '/jobs/analyze', [
                'job_id' => $job->id,
                'job_data' => [
                    'title' => $job->title,
                    'description' => $job->description,
                    'skills' => $job->skills->pluck('name')->toArray(),
                    'category' => $job->category,
                    'experience_level' => $job->experience_level,
                ]
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail job creation
            \Log::error('AI matching failed: ' . $e->getMessage());
        }
    }

    /**
     * Basic freelancer matching fallback
     */
    private function basicFreelancerMatching($job)
    {
        $freelancers = \App\Models\User::where('role', 'freelancer')
                                      ->whereHas('skills', function ($q) use ($job) {
                                          $q->whereIn('name', $job->skills->pluck('name'));
                                      })
                                      ->with(['skills', 'reviews'])
                                      ->limit(10)
                                      ->get();

        return response()->json([
            'matches' => $freelancers,
            'message' => 'Basic matching results'
        ]);
    }
}
