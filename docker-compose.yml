version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: workwise_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: workwise
      MYSQL_USER: workwise
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - workwise_network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: workwise_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - workwise_network

  # Laravel Backend API
  backend:
    build:
      context: ./workwise-backend
      dockerfile: Dockerfile
    container_name: workwise_backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=workwise
      - DB_USERNAME=workwise
      - DB_PASSWORD=password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AI_SERVICE_URL=http://ai:5000
    volumes:
      - ./workwise-backend:/var/www/html
      - backend_storage:/var/www/html/storage
    depends_on:
      - mysql
      - redis
    networks:
      - workwise_network

  # React Frontend
  frontend:
    build:
      context: ./workwise-frontend
      dockerfile: Dockerfile
    container_name: workwise_frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://localhost:8000/api
      - VITE_WS_URL=ws://localhost:6001
    volumes:
      - ./workwise-frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - workwise_network

  # Python AI Service
  ai:
    build:
      context: ./workwise-ai
      dockerfile: Dockerfile
    container_name: workwise_ai
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=mysql://workwise:password@mysql:3306/workwise
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./workwise-ai:/app
      - ai_models:/app/models
    depends_on:
      - mysql
      - redis
    networks:
      - workwise_network

  # Laravel WebSockets Server
  websockets:
    build:
      context: ./workwise-backend
      dockerfile: Dockerfile.websockets
    container_name: workwise_websockets
    restart: unless-stopped
    ports:
      - "6001:6001"
    environment:
      - APP_ENV=local
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=workwise
      - DB_USERNAME=workwise
      - DB_PASSWORD=password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./workwise-backend:/var/www/html
    depends_on:
      - mysql
      - redis
      - backend
    networks:
      - workwise_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: workwise_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/sites:/etc/nginx/sites-available
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
      - ai
    networks:
      - workwise_network

volumes:
  mysql_data:
  redis_data:
  backend_storage:
  ai_models:

networks:
  workwise_network:
    driver: bridge
