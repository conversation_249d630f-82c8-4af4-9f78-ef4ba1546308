import React, { createContext, useContext, useEffect, useState } from 'react'
import { io } from 'socket.io-client'
import { useAuth } from './AuthContext'

const SocketContext = createContext()

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null)
  const [connected, setConnected] = useState(false)
  const [notifications, setNotifications] = useState([])
  const { user, token } = useAuth()

  useEffect(() => {
    if (user && token) {
      // Initialize socket connection
      const newSocket = io(import.meta.env.VITE_WS_URL || 'ws://localhost:6001', {
        auth: {
          token: token
        },
        transports: ['websocket']
      })

      newSocket.on('connect', () => {
        console.log('Socket connected')
        setConnected(true)
      })

      newSocket.on('disconnect', () => {
        console.log('Socket disconnected')
        setConnected(false)
      })

      // Listen for notifications
      newSocket.on('notification', (notification) => {
        setNotifications(prev => [notification, ...prev.slice(0, 49)]) // Keep last 50
      })

      // Listen for new messages
      newSocket.on('new_message', (message) => {
        // Handle new message notification
        setNotifications(prev => [{
          id: Date.now(),
          type: 'message',
          title: 'New Message',
          message: `New message from ${message.sender.name}`,
          data: message,
          created_at: new Date().toISOString()
        }, ...prev.slice(0, 49)])
      })

      // Listen for job updates
      newSocket.on('job_update', (jobUpdate) => {
        setNotifications(prev => [{
          id: Date.now(),
          type: 'job_update',
          title: 'Job Update',
          message: jobUpdate.message,
          data: jobUpdate,
          created_at: new Date().toISOString()
        }, ...prev.slice(0, 49)])
      })

      // Listen for bid updates
      newSocket.on('bid_update', (bidUpdate) => {
        setNotifications(prev => [{
          id: Date.now(),
          type: 'bid_update',
          title: 'Bid Update',
          message: bidUpdate.message,
          data: bidUpdate,
          created_at: new Date().toISOString()
        }, ...prev.slice(0, 49)])
      })

      // Listen for contract updates
      newSocket.on('contract_update', (contractUpdate) => {
        setNotifications(prev => [{
          id: Date.now(),
          type: 'contract_update',
          title: 'Contract Update',
          message: contractUpdate.message,
          data: contractUpdate,
          created_at: new Date().toISOString()
        }, ...prev.slice(0, 49)])
      })

      setSocket(newSocket)

      return () => {
        newSocket.close()
        setSocket(null)
        setConnected(false)
      }
    }
  }, [user, token])

  const joinRoom = (roomId) => {
    if (socket) {
      socket.emit('join_room', roomId)
    }
  }

  const leaveRoom = (roomId) => {
    if (socket) {
      socket.emit('leave_room', roomId)
    }
  }

  const sendMessage = (roomId, message) => {
    if (socket) {
      socket.emit('send_message', { roomId, message })
    }
  }

  const markNotificationAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId 
          ? { ...notif, read: true }
          : notif
      )
    )
  }

  const clearNotifications = () => {
    setNotifications([])
  }

  const value = {
    socket,
    connected,
    notifications,
    joinRoom,
    leaveRoom,
    sendMessage,
    markNotificationAsRead,
    clearNotifications,
  }

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  )
}

export const useSocket = () => {
  const context = useContext(SocketContext)
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider')
  }
  return context
}
