import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import joblib
import os
import logging
from datetime import datetime
import re

logger = logging.getLogger(__name__)

class JobMatcher:
    def __init__(self):
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        self.scaler = StandardScaler()
        self.match_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.is_trained = False
        self.load_models()
        
    def load_models(self):
        """Load pre-trained models if they exist"""
        try:
            model_dir = 'models'
            if os.path.exists(f'{model_dir}/tfidf_vectorizer.pkl'):
                self.tfidf_vectorizer = joblib.load(f'{model_dir}/tfidf_vectorizer.pkl')
                self.scaler = joblib.load(f'{model_dir}/scaler.pkl')
                self.match_model = joblib.load(f'{model_dir}/match_model.pkl')
                self.is_trained = True
                logger.info("Loaded pre-trained models")
        except Exception as e:
            logger.warning(f"Could not load models: {e}")
            
    def save_models(self):
        """Save trained models"""
        try:
            model_dir = 'models'
            os.makedirs(model_dir, exist_ok=True)
            
            joblib.dump(self.tfidf_vectorizer, f'{model_dir}/tfidf_vectorizer.pkl')
            joblib.dump(self.scaler, f'{model_dir}/scaler.pkl')
            joblib.dump(self.match_model, f'{model_dir}/match_model.pkl')
            logger.info("Models saved successfully")
        except Exception as e:
            logger.error(f"Error saving models: {e}")
    
    def preprocess_text(self, text):
        """Clean and preprocess text data"""
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters but keep spaces
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text
    
    def extract_features(self, job_data, freelancer_data=None):
        """Extract features for matching"""
        features = {}
        
        # Job features
        if job_data:
            job_text = f"{job_data.get('title', '')} {job_data.get('description', '')}"
            features['job_text'] = self.preprocess_text(job_text)
            features['budget_min'] = float(job_data.get('budget_min', 0))
            features['budget_max'] = float(job_data.get('budget_max', 0))
            features['experience_level'] = job_data.get('experience_level', 'intermediate')
            features['job_type'] = job_data.get('job_type', 'fixed')
            features['skills'] = job_data.get('skills', [])
            
        # Freelancer features
        if freelancer_data:
            freelancer_text = f"{freelancer_data.get('bio', '')} {' '.join(freelancer_data.get('skills', []))}"
            features['freelancer_text'] = self.preprocess_text(freelancer_text)
            features['hourly_rate'] = float(freelancer_data.get('hourly_rate', 0))
            features['freelancer_skills'] = freelancer_data.get('skills', [])
            features['experience_years'] = freelancer_data.get('experience_years', 0)
            features['rating'] = freelancer_data.get('rating', 0)
            features['completion_rate'] = freelancer_data.get('completion_rate', 0)
            
        return features
    
    def calculate_skill_match(self, job_skills, freelancer_skills):
        """Calculate skill matching score"""
        if not job_skills or not freelancer_skills:
            return 0.0
        
        job_skills_set = set([skill.lower() for skill in job_skills])
        freelancer_skills_set = set([skill.lower() for skill in freelancer_skills])
        
        # Calculate Jaccard similarity
        intersection = len(job_skills_set.intersection(freelancer_skills_set))
        union = len(job_skills_set.union(freelancer_skills_set))
        
        if union == 0:
            return 0.0
        
        return intersection / union
    
    def calculate_budget_compatibility(self, job_budget_min, job_budget_max, freelancer_rate, job_type='fixed'):
        """Calculate budget compatibility score"""
        if job_type == 'hourly':
            if freelancer_rate == 0:
                return 0.5  # Neutral score if no rate specified
            
            # Check if freelancer rate is within job budget range
            if job_budget_min <= freelancer_rate <= job_budget_max:
                return 1.0
            elif freelancer_rate < job_budget_min:
                # Freelancer is cheaper than expected
                return 0.8
            else:
                # Freelancer is more expensive
                ratio = job_budget_max / freelancer_rate
                return max(0.0, ratio)
        else:
            # For fixed projects, assume 40 hours of work
            estimated_cost = freelancer_rate * 40
            if job_budget_min <= estimated_cost <= job_budget_max:
                return 1.0
            elif estimated_cost < job_budget_min:
                return 0.8
            else:
                ratio = job_budget_max / estimated_cost
                return max(0.0, ratio)
    
    def calculate_experience_match(self, job_experience_level, freelancer_experience_years):
        """Calculate experience level compatibility"""
        experience_mapping = {
            'entry': (0, 2),
            'intermediate': (2, 5),
            'expert': (5, float('inf'))
        }
        
        min_years, max_years = experience_mapping.get(job_experience_level, (0, float('inf')))
        
        if min_years <= freelancer_experience_years <= max_years:
            return 1.0
        elif freelancer_experience_years < min_years:
            # Under-qualified
            return max(0.0, freelancer_experience_years / min_years)
        else:
            # Over-qualified (still good but might be expensive)
            return 0.8
    
    def find_freelancer_matches(self, job_data, job_id=None, limit=10):
        """Find matching freelancers for a job"""
        try:
            # In a real implementation, this would query the database
            # For now, we'll simulate with mock data
            mock_freelancers = self._get_mock_freelancers()
            
            matches = []
            job_features = self.extract_features(job_data)
            
            for freelancer in mock_freelancers:
                freelancer_features = self.extract_features(None, freelancer)
                
                # Calculate various matching scores
                skill_score = self.calculate_skill_match(
                    job_features.get('skills', []),
                    freelancer_features.get('freelancer_skills', [])
                )
                
                budget_score = self.calculate_budget_compatibility(
                    job_features.get('budget_min', 0),
                    job_features.get('budget_max', 0),
                    freelancer_features.get('hourly_rate', 0),
                    job_features.get('job_type', 'fixed')
                )
                
                experience_score = self.calculate_experience_match(
                    job_features.get('experience_level', 'intermediate'),
                    freelancer_features.get('experience_years', 0)
                )
                
                # Text similarity using TF-IDF
                text_score = 0.0
                if self.is_trained:
                    try:
                        job_vector = self.tfidf_vectorizer.transform([job_features.get('job_text', '')])
                        freelancer_vector = self.tfidf_vectorizer.transform([freelancer_features.get('freelancer_text', '')])
                        text_score = cosine_similarity(job_vector, freelancer_vector)[0][0]
                    except:
                        text_score = 0.5
                
                # Calculate overall match score
                overall_score = (
                    skill_score * 0.4 +
                    budget_score * 0.25 +
                    experience_score * 0.2 +
                    text_score * 0.15
                )
                
                # Add freelancer rating and completion rate bonus
                rating_bonus = freelancer.get('rating', 0) / 5.0 * 0.1
                completion_bonus = freelancer.get('completion_rate', 0) / 100.0 * 0.05
                
                final_score = min(1.0, overall_score + rating_bonus + completion_bonus)
                
                matches.append({
                    'freelancer_id': freelancer['id'],
                    'freelancer_name': freelancer['name'],
                    'match_score': round(final_score, 3),
                    'skill_match': round(skill_score, 3),
                    'budget_compatibility': round(budget_score, 3),
                    'experience_match': round(experience_score, 3),
                    'text_similarity': round(text_score, 3),
                    'hourly_rate': freelancer.get('hourly_rate', 0),
                    'rating': freelancer.get('rating', 0),
                    'completion_rate': freelancer.get('completion_rate', 0),
                    'skills': freelancer.get('skills', []),
                    'bio': freelancer.get('bio', '')[:200] + '...' if len(freelancer.get('bio', '')) > 200 else freelancer.get('bio', '')
                })
            
            # Sort by match score and return top matches
            matches.sort(key=lambda x: x['match_score'], reverse=True)
            return matches[:limit]
            
        except Exception as e:
            logger.error(f"Error finding freelancer matches: {e}")
            return []
    
    def find_job_matches(self, freelancer_data, freelancer_id=None, limit=10):
        """Find matching jobs for a freelancer"""
        try:
            # In a real implementation, this would query the database
            # For now, we'll simulate with mock data
            mock_jobs = self._get_mock_jobs()
            
            matches = []
            freelancer_features = self.extract_features(None, freelancer_data)
            
            for job in mock_jobs:
                job_features = self.extract_features(job)
                
                # Calculate matching scores (similar to freelancer matching)
                skill_score = self.calculate_skill_match(
                    job_features.get('skills', []),
                    freelancer_features.get('freelancer_skills', [])
                )
                
                budget_score = self.calculate_budget_compatibility(
                    job_features.get('budget_min', 0),
                    job_features.get('budget_max', 0),
                    freelancer_features.get('hourly_rate', 0),
                    job_features.get('job_type', 'fixed')
                )
                
                experience_score = self.calculate_experience_match(
                    job_features.get('experience_level', 'intermediate'),
                    freelancer_features.get('experience_years', 0)
                )
                
                # Calculate overall match score
                overall_score = (
                    skill_score * 0.4 +
                    budget_score * 0.3 +
                    experience_score * 0.3
                )
                
                matches.append({
                    'job_id': job['id'],
                    'job_title': job['title'],
                    'match_score': round(overall_score, 3),
                    'skill_match': round(skill_score, 3),
                    'budget_compatibility': round(budget_score, 3),
                    'experience_match': round(experience_score, 3),
                    'budget_range': f"${job.get('budget_min', 0):.0f} - ${job.get('budget_max', 0):.0f}",
                    'job_type': job.get('job_type', 'fixed'),
                    'experience_level': job.get('experience_level', 'intermediate'),
                    'skills': job.get('skills', []),
                    'description': job.get('description', '')[:200] + '...' if len(job.get('description', '')) > 200 else job.get('description', '')
                })
            
            # Sort by match score and return top matches
            matches.sort(key=lambda x: x['match_score'], reverse=True)
            return matches[:limit]
            
        except Exception as e:
            logger.error(f"Error finding job matches: {e}")
            return []
    
    def calculate_job_complexity(self, job_data):
        """Calculate job complexity score"""
        complexity_score = 0.0
        
        # Factor in description length
        description_length = len(job_data.get('description', ''))
        if description_length > 1000:
            complexity_score += 0.3
        elif description_length > 500:
            complexity_score += 0.2
        else:
            complexity_score += 0.1
        
        # Factor in number of skills required
        skills_count = len(job_data.get('skills', []))
        if skills_count > 5:
            complexity_score += 0.3
        elif skills_count > 3:
            complexity_score += 0.2
        else:
            complexity_score += 0.1
        
        # Factor in experience level
        experience_level = job_data.get('experience_level', 'intermediate')
        if experience_level == 'expert':
            complexity_score += 0.4
        elif experience_level == 'intermediate':
            complexity_score += 0.2
        else:
            complexity_score += 0.1
        
        return min(1.0, complexity_score)
    
    def retrain_model(self):
        """Retrain the matching model with new data"""
        try:
            # In a real implementation, this would fetch training data from the database
            logger.info("Retraining job matching model...")
            # Placeholder for model retraining logic
            self.is_trained = True
            self.save_models()
            logger.info("Model retraining completed")
        except Exception as e:
            logger.error(f"Error retraining model: {e}")
    
    def get_performance_metrics(self):
        """Get model performance metrics"""
        return {
            'model_trained': self.is_trained,
            'last_updated': datetime.utcnow().isoformat(),
            'accuracy': 0.85,  # Placeholder
            'precision': 0.82,  # Placeholder
            'recall': 0.88,  # Placeholder
        }
    
    def _get_mock_freelancers(self):
        """Mock freelancer data for testing"""
        return [
            {
                'id': 1,
                'name': 'John Doe',
                'bio': 'Experienced React developer with 5 years of experience in building modern web applications',
                'skills': ['React', 'JavaScript', 'Node.js', 'MongoDB'],
                'hourly_rate': 50,
                'experience_years': 5,
                'rating': 4.8,
                'completion_rate': 95
            },
            {
                'id': 2,
                'name': 'Sarah Smith',
                'bio': 'Full-stack developer specializing in Python and Django with machine learning expertise',
                'skills': ['Python', 'Django', 'Machine Learning', 'PostgreSQL'],
                'hourly_rate': 60,
                'experience_years': 7,
                'rating': 4.9,
                'completion_rate': 98
            },
            {
                'id': 3,
                'name': 'Mike Johnson',
                'bio': 'Mobile app developer with expertise in React Native and Flutter',
                'skills': ['React Native', 'Flutter', 'iOS', 'Android'],
                'hourly_rate': 45,
                'experience_years': 4,
                'rating': 4.6,
                'completion_rate': 92
            }
        ]
    
    def _get_mock_jobs(self):
        """Mock job data for testing"""
        return [
            {
                'id': 1,
                'title': 'React Developer for E-commerce Website',
                'description': 'Looking for an experienced React developer to build a modern e-commerce website with payment integration',
                'skills': ['React', 'JavaScript', 'CSS', 'Payment Integration'],
                'budget_min': 2000,
                'budget_max': 4000,
                'job_type': 'fixed',
                'experience_level': 'intermediate'
            },
            {
                'id': 2,
                'title': 'Python Data Scientist for ML Project',
                'description': 'Need a data scientist to build machine learning models for customer behavior prediction',
                'skills': ['Python', 'Machine Learning', 'Pandas', 'Scikit-learn'],
                'budget_min': 3000,
                'budget_max': 6000,
                'job_type': 'fixed',
                'experience_level': 'expert'
            },
            {
                'id': 3,
                'title': 'Mobile App Development',
                'description': 'Develop a cross-platform mobile app for food delivery service',
                'skills': ['React Native', 'Mobile Development', 'API Integration'],
                'budget_min': 40,
                'budget_max': 60,
                'job_type': 'hourly',
                'experience_level': 'intermediate'
            }
        ]
