<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'bio',
        'skills',
        'hourly_rate',
        'location',
        'phone',
        'website',
        'avatar',
        'is_verified',
        'is_active',
        'last_active_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_active_at' => 'datetime',
        'skills' => 'array',
        'hourly_rate' => 'decimal:2',
        'is_verified' => 'boolean',
        'is_active' => 'boolean',
        'password' => 'hashed',
    ];

    /**
     * Get jobs posted by employer
     */
    public function jobs()
    {
        return $this->hasMany(Job::class, 'employer_id');
    }

    /**
     * Get bids submitted by freelancer
     */
    public function bids()
    {
        return $this->hasMany(Bid::class, 'freelancer_id');
    }

    /**
     * Get contracts as freelancer
     */
    public function freelancerContracts()
    {
        return $this->hasMany(Contract::class, 'freelancer_id');
    }

    /**
     * Get contracts as employer
     */
    public function employerContracts()
    {
        return $this->hasMany(Contract::class, 'employer_id');
    }

    /**
     * Get all contracts (freelancer or employer)
     */
    public function contracts()
    {
        return Contract::where('freelancer_id', $this->id)
                      ->orWhere('employer_id', $this->id);
    }

    /**
     * Get reviews received
     */
    public function reviewsReceived()
    {
        return $this->hasMany(Review::class, 'reviewee_id');
    }

    /**
     * Get reviews given
     */
    public function reviewsGiven()
    {
        return $this->hasMany(Review::class, 'reviewer_id');
    }

    /**
     * Get messages sent
     */
    public function messagesSent()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    /**
     * Get messages received
     */
    public function messagesReceived()
    {
        return $this->hasMany(Message::class, 'receiver_id');
    }

    /**
     * Get conversations
     */
    public function conversations()
    {
        return $this->belongsToMany(Conversation::class, 'conversation_participants')
                    ->withTimestamps();
    }

    /**
     * Get payments made (as employer)
     */
    public function paymentsMade()
    {
        return $this->hasMany(Payment::class, 'payer_id');
    }

    /**
     * Get payments received (as freelancer)
     */
    public function paymentsReceived()
    {
        return $this->hasMany(Payment::class, 'payee_id');
    }

    /**
     * Get user skills
     */
    public function userSkills()
    {
        return $this->belongsToMany(Skill::class, 'user_skills')
                    ->withPivot('proficiency_level', 'years_experience')
                    ->withTimestamps();
    }

    /**
     * Get notifications
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Check if user is freelancer
     */
    public function isFreelancer()
    {
        return $this->role === 'freelancer';
    }

    /**
     * Check if user is employer
     */
    public function isEmployer()
    {
        return $this->role === 'employer';
    }

    /**
     * Check if user is admin
     */
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    /**
     * Get average rating
     */
    public function getAverageRatingAttribute()
    {
        return $this->reviewsReceived()->avg('rating') ?? 0;
    }

    /**
     * Get total reviews count
     */
    public function getTotalReviewsAttribute()
    {
        return $this->reviewsReceived()->count();
    }

    /**
     * Get completion rate for freelancers
     */
    public function getCompletionRateAttribute()
    {
        if (!$this->isFreelancer()) {
            return null;
        }

        $totalContracts = $this->freelancerContracts()->count();
        if ($totalContracts === 0) {
            return 0;
        }

        $completedContracts = $this->freelancerContracts()
                                  ->where('status', 'completed')
                                  ->count();

        return round(($completedContracts / $totalContracts) * 100, 2);
    }

    /**
     * Get total earnings for freelancers
     */
    public function getTotalEarningsAttribute()
    {
        if (!$this->isFreelancer()) {
            return 0;
        }

        return $this->paymentsReceived()
                   ->where('status', 'completed')
                   ->sum('amount');
    }

    /**
     * Get total spent for employers
     */
    public function getTotalSpentAttribute()
    {
        if (!$this->isEmployer()) {
            return 0;
        }

        return $this->paymentsMade()
                   ->where('status', 'completed')
                   ->sum('amount');
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for verified users
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope for freelancers
     */
    public function scopeFreelancers($query)
    {
        return $query->where('role', 'freelancer');
    }

    /**
     * Scope for employers
     */
    public function scopeEmployers($query)
    {
        return $query->where('role', 'employer');
    }
}
