<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\JobController;
use App\Http\Controllers\BidController;
use App\Http\Controllers\ContractController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\SkillController;
use App\Http\Controllers\AIController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\ProfileController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);
});

// Public job browsing
Route::get('jobs', [JobController::class, 'index']);
Route::get('jobs/{id}', [JobController::class, 'show']);
Route::get('jobs/search', [JobController::class, 'search']);

// Public skills
Route::get('skills', [SkillController::class, 'index']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    
    // Authentication
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('profile', [AuthController::class, 'profile']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
    });

    // User Profile
    Route::prefix('profile')->group(function () {
        Route::get('/', [ProfileController::class, 'show']);
        Route::put('/', [ProfileController::class, 'update']);
        Route::post('avatar', [ProfileController::class, 'uploadAvatar']);
        Route::post('portfolio', [ProfileController::class, 'addPortfolioItem']);
        Route::delete('portfolio/{id}', [ProfileController::class, 'removePortfolioItem']);
    });

    // Jobs
    Route::prefix('jobs')->group(function () {
        Route::post('/', [JobController::class, 'store']);
        Route::put('{id}', [JobController::class, 'update']);
        Route::delete('{id}', [JobController::class, 'destroy']);
        Route::get('{id}/matches', [JobController::class, 'getMatches']);
        Route::post('{id}/watch', [JobController::class, 'watchJob']);
        Route::delete('{id}/watch', [JobController::class, 'unwatchJob']);
        Route::get('my-jobs', [JobController::class, 'myJobs']);
        Route::get('watched', [JobController::class, 'watchedJobs']);
    });

    // Bids
    Route::prefix('bids')->group(function () {
        Route::get('/', [BidController::class, 'index']);
        Route::get('{id}', [BidController::class, 'show']);
        Route::post('/', [BidController::class, 'store']);
        Route::put('{id}', [BidController::class, 'update']);
        Route::delete('{id}', [BidController::class, 'destroy']);
        Route::post('{id}/accept', [BidController::class, 'accept']);
        Route::post('{id}/reject', [BidController::class, 'reject']);
        Route::get('my-bids', [BidController::class, 'myBids']);
        Route::get('received', [BidController::class, 'receivedBids']);
    });

    // Contracts
    Route::prefix('contracts')->group(function () {
        Route::get('/', [ContractController::class, 'index']);
        Route::get('{id}', [ContractController::class, 'show']);
        Route::post('/', [ContractController::class, 'store']);
        Route::put('{id}', [ContractController::class, 'update']);
        Route::post('{id}/sign', [ContractController::class, 'sign']);
        Route::post('{id}/start', [ContractController::class, 'start']);
        Route::post('{id}/complete', [ContractController::class, 'complete']);
        Route::post('{id}/dispute', [ContractController::class, 'dispute']);
        Route::post('{id}/cancel', [ContractController::class, 'cancel']);
        Route::get('{id}/milestones', [ContractController::class, 'getMilestones']);
        Route::post('{id}/milestones', [ContractController::class, 'createMilestone']);
        Route::put('milestones/{id}', [ContractController::class, 'updateMilestone']);
    });

    // Messages
    Route::prefix('messages')->group(function () {
        Route::get('conversations', [MessageController::class, 'getConversations']);
        Route::get('conversations/{id}', [MessageController::class, 'getMessages']);
        Route::post('conversations', [MessageController::class, 'createConversation']);
        Route::post('conversations/{id}', [MessageController::class, 'sendMessage']);
        Route::post('conversations/{id}/read', [MessageController::class, 'markAsRead']);
        Route::delete('conversations/{id}', [MessageController::class, 'deleteConversation']);
        Route::get('unread-count', [MessageController::class, 'getUnreadCount']);
    });

    // Payments
    Route::prefix('payments')->group(function () {
        Route::get('/', [PaymentController::class, 'index']);
        Route::get('{id}', [PaymentController::class, 'show']);
        Route::post('intent', [PaymentController::class, 'createPaymentIntent']);
        Route::post('confirm', [PaymentController::class, 'confirmPayment']);
        Route::post('release', [PaymentController::class, 'releasePayment']);
        Route::post('{id}/refund', [PaymentController::class, 'refundPayment']);
        Route::get('earnings', [PaymentController::class, 'getEarnings']);
        Route::get('expenses', [PaymentController::class, 'getExpenses']);
        Route::post('withdraw', [PaymentController::class, 'withdrawEarnings']);
    });

    // Reviews
    Route::prefix('reviews')->group(function () {
        Route::get('/', [ReviewController::class, 'index']);
        Route::post('/', [ReviewController::class, 'store']);
        Route::put('{id}', [ReviewController::class, 'update']);
        Route::delete('{id}', [ReviewController::class, 'destroy']);
        Route::get('received', [ReviewController::class, 'receivedReviews']);
        Route::get('given', [ReviewController::class, 'givenReviews']);
    });

    // Skills
    Route::prefix('skills')->group(function () {
        Route::post('/', [SkillController::class, 'store']);
        Route::put('{id}', [SkillController::class, 'update']);
        Route::delete('{id}', [SkillController::class, 'destroy']);
        Route::post('user-skills', [SkillController::class, 'addUserSkill']);
        Route::delete('user-skills/{id}', [SkillController::class, 'removeUserSkill']);
    });

    // AI Recommendations
    Route::prefix('ai')->group(function () {
        Route::get('recommendations/jobs/{userId}', [AIController::class, 'getJobRecommendations']);
        Route::get('matches/freelancers/{jobId}', [AIController::class, 'getFreelancerMatches']);
        Route::post('analyze/profile', [AIController::class, 'analyzeProfile']);
        Route::post('analyze/job', [AIController::class, 'analyzeJob']);
        Route::get('recommendations/skills/{userId}', [AIController::class, 'getSkillRecommendations']);
        Route::get('recommendations/pricing/{userId}', [AIController::class, 'getPricingRecommendations']);
    });

    // Notifications
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::post('{id}/read', [NotificationController::class, 'markAsRead']);
        Route::post('read-all', [NotificationController::class, 'markAllAsRead']);
        Route::delete('{id}', [NotificationController::class, 'destroy']);
        Route::get('unread-count', [NotificationController::class, 'getUnreadCount']);
    });

    // Dashboard
    Route::get('dashboard/stats', function (Request $request) {
        $user = $request->user();
        
        if ($user->role === 'freelancer') {
            return response()->json([
                'active_projects' => $user->freelancerContracts()->where('status', 'active')->count(),
                'total_earnings' => $user->total_earnings,
                'completed_jobs' => $user->freelancerContracts()->where('status', 'completed')->count(),
                'average_rating' => $user->average_rating,
                'completion_rate' => $user->completion_rate,
            ]);
        } else {
            return response()->json([
                'active_jobs' => $user->jobs()->where('status', 'active')->count(),
                'total_spent' => $user->total_spent,
                'hired_freelancers' => $user->employerContracts()->distinct('freelancer_id')->count(),
                'success_rate' => 95, // Calculate based on completed projects
            ]);
        }
    });

    // Admin routes
    Route::middleware('admin')->prefix('admin')->group(function () {
        Route::get('users', [AdminController::class, 'getUsers']);
        Route::get('users/{id}', [AdminController::class, 'getUser']);
        Route::put('users/{id}', [AdminController::class, 'updateUser']);
        Route::delete('users/{id}', [AdminController::class, 'deleteUser']);
        Route::post('users/{id}/verify', [AdminController::class, 'verifyUser']);
        Route::post('users/{id}/suspend', [AdminController::class, 'suspendUser']);
        
        Route::get('jobs', [AdminController::class, 'getJobs']);
        Route::put('jobs/{id}', [AdminController::class, 'updateJob']);
        Route::delete('jobs/{id}', [AdminController::class, 'deleteJob']);
        
        Route::get('contracts', [AdminController::class, 'getContracts']);
        Route::get('payments', [AdminController::class, 'getPayments']);
        Route::get('disputes', [AdminController::class, 'getDisputes']);
        Route::post('disputes/{id}/resolve', [AdminController::class, 'resolveDispute']);
        
        Route::get('analytics', [AdminController::class, 'getAnalytics']);
        Route::get('reports/revenue', [AdminController::class, 'getRevenueReport']);
        Route::get('reports/users', [AdminController::class, 'getUserReport']);
        Route::get('reports/jobs', [AdminController::class, 'getJobReport']);
    });
});

// Webhook routes (no authentication required)
Route::prefix('webhooks')->group(function () {
    Route::post('stripe', [PaymentController::class, 'stripeWebhook']);
});

// Health check
Route::get('health', function () {
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now()->toISOString(),
        'service' => 'WorkWise API'
    ]);
});
