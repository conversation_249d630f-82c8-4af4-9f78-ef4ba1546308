import React from 'react'
import { useAuth } from '../contexts/AuthContext'
import { 
  Briefcase, 
  DollarSign, 
  Users, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Star
} from 'lucide-react'

const Dashboard = () => {
  const { user } = useAuth()

  // Mock data - replace with real API calls
  const stats = user?.role === 'freelancer' 
    ? [
        { label: 'Active Projects', value: '3', icon: Briefcase, color: 'text-blue-600' },
        { label: 'Total Earnings', value: '$2,450', icon: DollarSign, color: 'text-green-600' },
        { label: 'Completed Jobs', value: '12', icon: CheckCircle, color: 'text-purple-600' },
        { label: 'Rating', value: '4.8', icon: Star, color: 'text-yellow-600' },
      ]
    : [
        { label: 'Active Jobs', value: '5', icon: Briefcase, color: 'text-blue-600' },
        { label: 'Total Spent', value: '$8,200', icon: DollarSign, color: 'text-green-600' },
        { label: 'Hired Freelancers', value: '8', icon: Users, color: 'text-purple-600' },
        { label: 'Success Rate', value: '95%', icon: TrendingUp, color: 'text-yellow-600' },
      ]

  const recentActivity = [
    {
      id: 1,
      type: 'job_completed',
      title: 'Website Design Project Completed',
      description: 'John Doe completed the e-commerce website design',
      time: '2 hours ago',
      icon: CheckCircle,
      color: 'text-green-600'
    },
    {
      id: 2,
      type: 'new_bid',
      title: 'New Bid Received',
      description: 'Sarah Smith submitted a bid for Mobile App Development',
      time: '4 hours ago',
      icon: AlertCircle,
      color: 'text-blue-600'
    },
    {
      id: 3,
      type: 'payment',
      title: 'Payment Received',
      description: 'Payment of $500 received for Logo Design project',
      time: '1 day ago',
      icon: DollarSign,
      color: 'text-green-600'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.name}!
          </h1>
          <p className="text-gray-600 mt-2">
            Here's what's happening with your {user?.role === 'freelancer' ? 'freelance work' : 'projects'} today.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg bg-gray-100 ${stat.color}`}>
                  <stat.icon className="h-6 w-6" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className={`p-2 rounded-full bg-gray-100 ${activity.color}`}>
                        <activity.icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">
                          {activity.title}
                        </p>
                        <p className="text-sm text-gray-500">
                          {activity.description}
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          {activity.time}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div>
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Quick Actions</h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  {user?.role === 'freelancer' ? (
                    <>
                      <button className="w-full btn-primary">
                        Browse Jobs
                      </button>
                      <button className="w-full btn-outline">
                        Update Profile
                      </button>
                      <button className="w-full btn-outline">
                        View Messages
                      </button>
                    </>
                  ) : (
                    <>
                      <button className="w-full btn-primary">
                        Post New Job
                      </button>
                      <button className="w-full btn-outline">
                        View Applications
                      </button>
                      <button className="w-full btn-outline">
                        Manage Contracts
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* AI Recommendations */}
            <div className="bg-white rounded-lg shadow mt-6">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">AI Recommendations</h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  {user?.role === 'freelancer' ? (
                    <>
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm font-medium text-blue-900">
                          Perfect Match Found!
                        </p>
                        <p className="text-sm text-blue-700">
                          React Developer position matches your skills
                        </p>
                      </div>
                      <div className="p-3 bg-green-50 rounded-lg">
                        <p className="text-sm font-medium text-green-900">
                          Skill Suggestion
                        </p>
                        <p className="text-sm text-green-700">
                          Consider adding TypeScript to increase job matches
                        </p>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm font-medium text-blue-900">
                          Top Candidate
                        </p>
                        <p className="text-sm text-blue-700">
                          Sarah M. is a 98% match for your React project
                        </p>
                      </div>
                      <div className="p-3 bg-purple-50 rounded-lg">
                        <p className="text-sm font-medium text-purple-900">
                          Budget Optimization
                        </p>
                        <p className="text-sm text-purple-700">
                          Consider increasing budget by 15% for better candidates
                        </p>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
