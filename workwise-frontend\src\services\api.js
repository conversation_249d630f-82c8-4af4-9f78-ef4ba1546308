import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (profileData) => api.put('/auth/profile', profileData),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (data) => api.post('/auth/reset-password', data),
}

// Jobs API
export const jobsAPI = {
  getJobs: (params) => api.get('/jobs', { params }),
  getJob: (id) => api.get(`/jobs/${id}`),
  createJob: (jobData) => api.post('/jobs', jobData),
  updateJob: (id, jobData) => api.put(`/jobs/${id}`, jobData),
  deleteJob: (id) => api.delete(`/jobs/${id}`),
  getJobMatches: (jobId) => api.get(`/jobs/${jobId}/matches`),
  searchJobs: (query) => api.get('/jobs/search', { params: { q: query } }),
}

// Bids API
export const bidsAPI = {
  getBids: (params) => api.get('/bids', { params }),
  getBid: (id) => api.get(`/bids/${id}`),
  createBid: (bidData) => api.post('/bids', bidData),
  updateBid: (id, bidData) => api.put(`/bids/${id}`, bidData),
  deleteBid: (id) => api.delete(`/bids/${id}`),
  acceptBid: (id) => api.post(`/bids/${id}/accept`),
  rejectBid: (id) => api.post(`/bids/${id}/reject`),
}

// Contracts API
export const contractsAPI = {
  getContracts: (params) => api.get('/contracts', { params }),
  getContract: (id) => api.get(`/contracts/${id}`),
  createContract: (contractData) => api.post('/contracts', contractData),
  updateContract: (id, contractData) => api.put(`/contracts/${id}`, contractData),
  signContract: (id) => api.post(`/contracts/${id}/sign`),
  completeContract: (id) => api.post(`/contracts/${id}/complete`),
  disputeContract: (id, reason) => api.post(`/contracts/${id}/dispute`, { reason }),
}

// Messages API
export const messagesAPI = {
  getConversations: () => api.get('/messages/conversations'),
  getMessages: (conversationId, params) => api.get(`/messages/conversations/${conversationId}`, { params }),
  sendMessage: (conversationId, messageData) => api.post(`/messages/conversations/${conversationId}`, messageData),
  markAsRead: (conversationId) => api.post(`/messages/conversations/${conversationId}/read`),
  createConversation: (participantId) => api.post('/messages/conversations', { participant_id: participantId }),
}

// Payments API
export const paymentsAPI = {
  getPayments: (params) => api.get('/payments', { params }),
  getPayment: (id) => api.get(`/payments/${id}`),
  createPaymentIntent: (amount, contractId) => api.post('/payments/intent', { amount, contract_id: contractId }),
  confirmPayment: (paymentIntentId) => api.post('/payments/confirm', { payment_intent_id: paymentIntentId }),
  releasePayment: (contractId) => api.post('/payments/release', { contract_id: contractId }),
  refundPayment: (paymentId, reason) => api.post(`/payments/${paymentId}/refund`, { reason }),
}

// Reviews API
export const reviewsAPI = {
  getReviews: (params) => api.get('/reviews', { params }),
  createReview: (reviewData) => api.post('/reviews', reviewData),
  updateReview: (id, reviewData) => api.put(`/reviews/${id}`, reviewData),
  deleteReview: (id) => api.delete(`/reviews/${id}`),
}

// Skills API
export const skillsAPI = {
  getSkills: () => api.get('/skills'),
  createSkill: (skillData) => api.post('/skills', skillData),
  updateSkill: (id, skillData) => api.put(`/skills/${id}`, skillData),
  deleteSkill: (id) => api.delete(`/skills/${id}`),
}

// AI API
export const aiAPI = {
  getJobRecommendations: (userId) => api.get(`/ai/recommendations/jobs/${userId}`),
  getFreelancerMatches: (jobId) => api.get(`/ai/matches/freelancers/${jobId}`),
  analyzeProfile: (profileData) => api.post('/ai/analyze/profile', profileData),
  analyzeJob: (jobData) => api.post('/ai/analyze/job', jobData),
}

// Admin API
export const adminAPI = {
  getUsers: (params) => api.get('/admin/users', { params }),
  getUser: (id) => api.get(`/admin/users/${id}`),
  updateUser: (id, userData) => api.put(`/admin/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/admin/users/${id}`),
  getJobs: (params) => api.get('/admin/jobs', { params }),
  getContracts: (params) => api.get('/admin/contracts', { params }),
  getPayments: (params) => api.get('/admin/payments', { params }),
  getAnalytics: () => api.get('/admin/analytics'),
  handleDispute: (disputeId, action) => api.post(`/admin/disputes/${disputeId}`, { action }),
}

export default api
