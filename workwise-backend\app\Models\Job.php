<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Job extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'employer_id',
        'title',
        'description',
        'category',
        'job_type',
        'budget_min',
        'budget_max',
        'deadline',
        'location',
        'experience_level',
        'project_duration',
        'status',
        'featured',
        'urgent',
        'remote_allowed',
        'attachments',
        'requirements',
        'deliverables',
        'ai_score',
        'views_count',
        'applications_count',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'deadline' => 'datetime',
        'budget_min' => 'decimal:2',
        'budget_max' => 'decimal:2',
        'featured' => 'boolean',
        'urgent' => 'boolean',
        'remote_allowed' => 'boolean',
        'attachments' => 'array',
        'requirements' => 'array',
        'deliverables' => 'array',
        'ai_score' => 'decimal:2',
        'views_count' => 'integer',
        'applications_count' => 'integer',
    ];

    /**
     * Get the employer that posted the job
     */
    public function employer()
    {
        return $this->belongsTo(User::class, 'employer_id');
    }

    /**
     * Get bids for this job
     */
    public function bids()
    {
        return $this->hasMany(Bid::class);
    }

    /**
     * Get accepted bid for this job
     */
    public function acceptedBid()
    {
        return $this->hasOne(Bid::class)->where('status', 'accepted');
    }

    /**
     * Get contract for this job
     */
    public function contract()
    {
        return $this->hasOne(Contract::class);
    }

    /**
     * Get job skills
     */
    public function skills()
    {
        return $this->belongsToMany(Skill::class, 'job_skills')
                    ->withPivot('importance_level')
                    ->withTimestamps();
    }

    /**
     * Get job categories
     */
    public function categories()
    {
        return $this->belongsToMany(Category::class, 'job_categories')
                    ->withTimestamps();
    }

    /**
     * Get job watchers (users who saved/bookmarked the job)
     */
    public function watchers()
    {
        return $this->belongsToMany(User::class, 'job_watchers')
                    ->withTimestamps();
    }

    /**
     * Get job views
     */
    public function views()
    {
        return $this->hasMany(JobView::class);
    }

    /**
     * Get AI recommendations for this job
     */
    public function aiRecommendations()
    {
        return $this->hasMany(AIRecommendation::class);
    }

    /**
     * Check if job is active
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if job is closed
     */
    public function isClosed()
    {
        return $this->status === 'closed';
    }

    /**
     * Check if job is completed
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * Check if job has deadline passed
     */
    public function isExpired()
    {
        return $this->deadline && $this->deadline->isPast();
    }

    /**
     * Check if user can bid on this job
     */
    public function canBid(User $user)
    {
        if (!$user->isFreelancer()) {
            return false;
        }

        if ($this->employer_id === $user->id) {
            return false;
        }

        if (!$this->isActive()) {
            return false;
        }

        if ($this->isExpired()) {
            return false;
        }

        // Check if user already has a bid
        return !$this->bids()->where('freelancer_id', $user->id)->exists();
    }

    /**
     * Get budget range as string
     */
    public function getBudgetRangeAttribute()
    {
        if ($this->job_type === 'fixed') {
            return '$' . number_format($this->budget_min) . ' - $' . number_format($this->budget_max);
        } else {
            return '$' . number_format($this->budget_min) . ' - $' . number_format($this->budget_max) . '/hr';
        }
    }

    /**
     * Get time remaining until deadline
     */
    public function getTimeRemainingAttribute()
    {
        if (!$this->deadline) {
            return null;
        }

        return $this->deadline->diffForHumans();
    }

    /**
     * Get average bid amount
     */
    public function getAverageBidAttribute()
    {
        return $this->bids()->avg('amount') ?? 0;
    }

    /**
     * Get lowest bid amount
     */
    public function getLowestBidAttribute()
    {
        return $this->bids()->min('amount') ?? 0;
    }

    /**
     * Get highest bid amount
     */
    public function getHighestBidAttribute()
    {
        return $this->bids()->max('amount') ?? 0;
    }

    /**
     * Scope for active jobs
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for featured jobs
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope for urgent jobs
     */
    public function scopeUrgent($query)
    {
        return $query->where('urgent', true);
    }

    /**
     * Scope for remote jobs
     */
    public function scopeRemote($query)
    {
        return $query->where('remote_allowed', true);
    }

    /**
     * Scope for jobs by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for jobs by budget range
     */
    public function scopeByBudgetRange($query, $min, $max)
    {
        return $query->where('budget_min', '>=', $min)
                    ->where('budget_max', '<=', $max);
    }

    /**
     * Scope for jobs by experience level
     */
    public function scopeByExperienceLevel($query, $level)
    {
        return $query->where('experience_level', $level);
    }

    /**
     * Scope for jobs with skills
     */
    public function scopeWithSkills($query, $skills)
    {
        return $query->whereHas('skills', function ($q) use ($skills) {
            $q->whereIn('name', $skills);
        });
    }

    /**
     * Increment views count
     */
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    /**
     * Increment applications count
     */
    public function incrementApplications()
    {
        $this->increment('applications_count');
    }
}
