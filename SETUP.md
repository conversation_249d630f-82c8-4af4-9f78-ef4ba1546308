# WorkWise Setup Guide

This guide will help you set up the WorkWise AI-driven gig marketplace on your local development environment.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v18 or higher)
- **PHP** (v8.2 or higher)
- **Composer** (PHP package manager)
- **MySQL** (v8.0 or higher)
- **Python** (v3.9 or higher)
- **Docker & Docker Compose** (optional, for containerized setup)

## Quick Start (Recommended)

### Option 1: Docker Setup (Easiest)

1. **Clone and navigate to the project:**
   ```bash
   cd WorkWise
   ```

2. **Start all services with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

3. **Wait for all services to start** (this may take a few minutes on first run)

4. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - AI Service: http://localhost:5000

### Option 2: Manual Setup

#### 1. Database Setup

1. **Create MySQL database:**
   ```sql
   CREATE DATABASE workwise;
   CREATE USER 'workwise'@'localhost' IDENTIFIED BY 'password';
   GRANT ALL PRIVILEGES ON workwise.* TO 'workwise'@'localhost';
   FLUSH PRIVILEGES;
   ```

#### 2. Backend Setup (Laravel)

1. **Navigate to backend directory:**
   ```bash
   cd workwise-backend
   ```

2. **Install PHP dependencies:**
   ```bash
   composer install
   ```

3. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

4. **Generate application key:**
   ```bash
   php artisan key:generate
   ```

5. **Configure database in .env file:**
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=workwise
   DB_USERNAME=workwise
   DB_PASSWORD=password
   ```

6. **Run database migrations:**
   ```bash
   php artisan migrate
   ```

7. **Seed the database (optional):**
   ```bash
   php artisan db:seed
   ```

8. **Start the Laravel server:**
   ```bash
   php artisan serve
   ```

#### 3. Frontend Setup (React)

1. **Navigate to frontend directory:**
   ```bash
   cd workwise-frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

4. **Start the development server:**
   ```bash
   npm run dev
   ```

#### 4. AI Service Setup (Python)

1. **Navigate to AI service directory:**
   ```bash
   cd workwise-ai
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

5. **Start the AI service:**
   ```bash
   python app.py
   ```

## Configuration

### Environment Variables

#### Backend (.env)
```env
APP_NAME=WorkWise
APP_ENV=local
APP_KEY=base64:your_generated_key
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=workwise
DB_USERNAME=workwise
DB_PASSWORD=password

# Stripe Configuration
STRIPE_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET=sk_test_your_stripe_secret_key

# AI Service
AI_SERVICE_URL=http://localhost:5000

# CORS
FRONTEND_URL=http://localhost:3000
```

#### Frontend (.env)
```env
VITE_API_URL=http://localhost:8000/api
VITE_WS_URL=ws://localhost:6001
VITE_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
```

#### AI Service (.env)
```env
FLASK_ENV=development
DATABASE_URL=mysql://workwise:password@localhost:3306/workwise
REDIS_URL=redis://localhost:6379/0
```

## Testing the Setup

1. **Access the frontend:** http://localhost:3000
2. **Register a new account** (choose either Freelancer or Employer)
3. **Login and explore the dashboard**
4. **Test API endpoints:** http://localhost:8000/api/health
5. **Test AI service:** http://localhost:5000/health

## Default Test Accounts

After running the seeders, you can use these test accounts:

**Admin:**
- Email: <EMAIL>
- Password: password

**Freelancer:**
- Email: <EMAIL>
- Password: password

**Employer:**
- Email: <EMAIL>
- Password: password

## Features Available

### ✅ Implemented
- User authentication (register, login, logout)
- Role-based access (freelancer, employer, admin)
- Responsive dashboard
- Job browsing structure
- AI service foundation
- Real-time notifications setup
- Payment integration structure
- Database schema

### 🚧 In Development
- Complete job posting and bidding system
- Real-time messaging
- AI-powered job matching
- Payment processing with Stripe
- File upload and portfolio management
- Advanced search and filtering
- Rating and review system

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Check what's using the port
   netstat -tulpn | grep :3000
   # Kill the process or use a different port
   ```

2. **Database connection error:**
   - Verify MySQL is running
   - Check database credentials in .env
   - Ensure database exists

3. **Composer/NPM install fails:**
   - Clear cache: `composer clear-cache` or `npm cache clean --force`
   - Delete vendor/node_modules and reinstall

4. **AI service fails to start:**
   - Ensure Python virtual environment is activated
   - Install missing system dependencies
   - Check Python version compatibility

### Getting Help

If you encounter issues:

1. Check the logs in each service
2. Verify all prerequisites are installed
3. Ensure all environment variables are set correctly
4. Check that all required ports are available

## Next Steps

1. **Configure Stripe** for payment processing
2. **Set up email** for notifications
3. **Configure file storage** for uploads
4. **Set up SSL** for production
5. **Configure CI/CD** pipeline

## Development Workflow

1. **Make changes** to your preferred service
2. **Test locally** using the development servers
3. **Run tests** (when implemented)
4. **Commit changes** to version control
5. **Deploy** using Docker Compose

## Production Deployment

For production deployment:

1. Use environment-specific .env files
2. Set up proper SSL certificates
3. Configure production database
4. Set up monitoring and logging
5. Use a process manager like PM2 for Node.js
6. Configure reverse proxy (Nginx)

---

**Happy coding! 🚀**

For more detailed documentation, check the individual README files in each service directory.
