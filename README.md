# WorkWise: AI-Driven Gig Marketplace

An intelligent gig marketplace platform that connects freelancers with employers using AI-driven job matching, real-time communication, secure contract management, and payment handling.

## 🚀 Features

- **AI-Powered Job Matching**: Smart matching algorithm using Python + Scikit-learn
- **Real-Time Communication**: WebSocket-based chat and notifications
- **Secure Payments**: Stripe integration with escrow services
- **Bidding System**: Competitive bidding with automated contract generation
- **Rating & Reviews**: Transparent feedback system
- **Admin Dashboard**: Comprehensive management panel

## 🛠 Tech Stack

### Frontend
- React.js with Vite
- Tailwind CSS
- React Router
- Socket.IO Client
- Axios for API calls

### Backend
- Laravel 11
- Laravel Sanctum (Authentication)
- Laravel WebSockets
- MySQL Database
- RESTful API

### AI & Machine Learning
- Python Flask/FastAPI
- Scikit-learn
- Natural Language Processing

### Payment & Security
- Stripe Payment Gateway
- JWT Authentication
- SSL Encryption

### DevOps
- Docker
- GitHub Actions (CI/CD)
- Heroku/DigitalOcean Deployment

## 📁 Project Structure

```
WorkWise/
├── workwise-frontend/          # React.js Frontend
├── workwise-backend/           # Laravel Backend API
├── workwise-ai/               # Python AI Service
├── docker-compose.yml         # Docker configuration
└── README.md                  # This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PHP 8.2+
- Composer
- Python 3.9+
- MySQL 8.0+

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd WorkWise
   ```

2. **Setup Frontend**
   ```bash
   cd workwise-frontend
   npm install
   npm run dev
   ```

3. **Setup Backend**
   ```bash
   cd workwise-backend
   composer install
   cp .env.example .env
   php artisan key:generate
   php artisan migrate
   php artisan serve
   ```

4. **Setup AI Service**
   ```bash
   cd workwise-ai
   pip install -r requirements.txt
   python app.py
   ```

## 🔧 Configuration

### Environment Variables

Create `.env` files in each service directory with the following configurations:

#### Backend (.env)
```
APP_NAME=WorkWise
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=workwise
DB_USERNAME=root
DB_PASSWORD=

STRIPE_KEY=your_stripe_public_key
STRIPE_SECRET=your_stripe_secret_key

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1
```

#### Frontend (.env)
```
VITE_API_URL=http://localhost:8000/api
VITE_WS_URL=ws://localhost:6001
VITE_STRIPE_PUBLIC_KEY=your_stripe_public_key
```

## 📚 API Documentation

The API follows RESTful conventions with the following main endpoints:

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/jobs` - List jobs
- `POST /api/jobs` - Create job
- `POST /api/bids` - Submit bid
- `GET /api/matches` - AI job matches

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, email <EMAIL> or join our Slack channel.
