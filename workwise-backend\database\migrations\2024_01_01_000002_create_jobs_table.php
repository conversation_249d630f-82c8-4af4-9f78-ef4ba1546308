<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jobs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employer_id')->constrained('users')->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->string('category');
            $table->enum('job_type', ['fixed', 'hourly']);
            $table->decimal('budget_min', 10, 2);
            $table->decimal('budget_max', 10, 2);
            $table->timestamp('deadline')->nullable();
            $table->string('location')->nullable();
            $table->enum('experience_level', ['entry', 'intermediate', 'expert']);
            $table->string('project_duration')->nullable();
            $table->enum('status', ['active', 'paused', 'closed', 'completed'])->default('active');
            $table->boolean('featured')->default(false);
            $table->boolean('urgent')->default(false);
            $table->boolean('remote_allowed')->default(true);
            $table->json('attachments')->nullable();
            $table->json('requirements')->nullable();
            $table->json('deliverables')->nullable();
            $table->decimal('ai_score', 5, 2)->nullable();
            $table->integer('views_count')->default(0);
            $table->integer('applications_count')->default(0);
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['status', 'featured']);
            $table->index(['category', 'status']);
            $table->index(['experience_level', 'status']);
            $table->index(['deadline', 'status']);
            $table->index('ai_score');
            $table->fullText(['title', 'description']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jobs');
    }
};
